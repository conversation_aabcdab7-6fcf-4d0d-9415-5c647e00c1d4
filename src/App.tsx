import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Layout } from "./components/Layout/Layout";
import { HomeZeroState } from "./screens/HomeZeroState/HomeZeroState";
import { ChatSessions } from "./screens/ChatSessions/ChatSessions";
import { NewSession } from "./screens/NewSession/NewSession";
import OnboardingPage from "./screens/Onboarding";
import AgentsPage from "./screens/Agents";
import Executions from "./screens/Executions";
import IntegrationsPage from "./screens/Integrations";
import SettingsLayout from "./screens/SettingsLayout";
import ToolConfigurationAgent from "./screens/ToolConfigurationAgent";
import KnowledgeGraphPage from "./screens/KnowledgeGraph";
import { ThemeProvider } from "./contexts/ThemeContext";

function App() {
  return (
    <ThemeProvider>
      <Router>
        <Routes>
          <Route
            path="/"
            element={
              <Layout title="Welcome, <PERSON>!" subtitle="Senior Platform Engineer">
                <HomeZeroState />
              </Layout>
            }
          />
          <Route
            path="/chat-sessions"
            element={
              <Layout title="My Chat Sessions" subtitle="Manage and review your conversation history">
                <ChatSessions />
              </Layout>
            }
          />
          <Route
            path="/chat-sessions/new"
            element={
              <Layout title="New Chat Session" subtitle="Start a conversation with an AccelOS AI Agent">
                <NewSession />
              </Layout>
            }
          />
          {/* Onboarding route - first under AUTOMATE SDLC */}
          <Route
            path="/onboarding"
            element={
              <Layout title="Team Onboarding" subtitle="Set up your team with AccelOS AI Agents">
                <OnboardingPage />
              </Layout>
            }
          />
          {/* AI Agents route - second under AUTOMATE SDLC */}
          <Route
            path="/agents"
            element={
              <Layout title="AI Agents" subtitle="Automate your SDLC workflows">
                <AgentsPage />
              </Layout>
            }
          />
          <Route
            path="/agents/tool-configuration"
            element={
              <Layout title="Tool Configuration Agent" subtitle="Configure your SDLC tools as per your org's best practices">
                <ToolConfigurationAgent />
              </Layout>
            }
          />
          {/* Executions route - third under AUTOMATE SDLC */}
          <Route
            path="/executions"
            element={
              <Layout title="Executions" subtitle="Monitor your AI Agent executions">
                <Executions />
              </Layout>
            }
          />
          <Route
            path="/knowledge-graph"
            element={
              <Layout title="Engineering Knowledge Graph" subtitle="Explore your engineering team and organizational knowledge">
                <KnowledgeGraphPage />
              </Layout>
            }
          />
          <Route
            path="/scorecards"
            element={
              <Layout title="Scorecards" subtitle="Assess software asset maturity">
                <div className="p-8 text-center text-[var(--text-primary)]">
                  <h2 className="text-2xl font-bold mb-4">Scorecards</h2>
                  <p className="text-[var(--text-tertiary)]">Coming soon...</p>
                </div>
              </Layout>
            }
          />
          {/* Redirect /settings to /settings/tool-integrations */}
          <Route path="/settings" element={<SettingsLayout><IntegrationsPage /></SettingsLayout>} />
          <Route path="/settings/tool-integrations" element={<SettingsLayout><IntegrationsPage /></SettingsLayout>} />
          <Route path="/settings/runners" element={<SettingsLayout><div className="p-8 text-center text-[var(--text-primary)]"><h1 className="font-['Manrope',Helvetica] font-bold text-[var(--text-primary)] text-lg leading-[22.3px] mb-1">Account Settings &gt; Runners</h1><p className="font-['Figtree',Helvetica] font-normal text-[var(--text-secondary)] text-sm leading-[19.6px] mb-4">Coming soon...</p></div></SettingsLayout>} />
          <Route
            path="/help"
            element={
              <Layout title="Help" subtitle="Get support and documentation">
                <div className="p-8 text-center text-[var(--text-primary)]">
                  <h2 className="text-2xl font-bold mb-4">Help</h2>
                  <p className="text-[var(--text-tertiary)]">Coming soon...</p>
                </div>
              </Layout>
            }
          />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;