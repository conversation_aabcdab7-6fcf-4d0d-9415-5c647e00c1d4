import React, { useState } from 'react';
import { SearchIcon, FilterIcon, PlusIcon } from 'lucide-react';
import { Card, CardContent } from '../../components/ui/card';
import { useTheme } from '../../contexts/ThemeContext';

// Integration data organized by categories with working company logos
const integrationCategories = [
  {
    title: "AI Coding Agent",
    integrations: [
      { name: "<PERSON><PERSON><PERSON>", logo: "https://avatars.githubusercontent.com/u/*********?s=200&v=4", connected: true },
      { name: "Anthropic Claude Code", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/anthropic.svg", connected: false },
      { name: "OpenAI Codex", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/openai.svg", connected: false },
      { name: "Augment Code", logo: "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/latest/assetbyname/Microsoft.VisualStudio.Services.Icons.Default", connected: false },
      { name: "Windsurf", logo: "https://avatars.githubusercontent.com/u/*********?s=200&v=4", connected: false },
      { name: "Cognition Devin", logo: "https://avatars.githubusercontent.com/u/*********?s=200&v=4", connected: false },
      { name: "GitHub Copilot", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/githubcopilot.svg", connected: true },
    ]
  },
  {
    title: "AI Code Review Agent",
    integrations: [
      { name: "Graphite.dev", logo: "https://avatars.githubusercontent.com/u/101845286?s=200&v=4", connected: false },
      { name: "CodeRabbit", logo: "https://avatars.githubusercontent.com/u/132044815?s=200&v=4", connected: true },
    ]
  },
  {
    title: "Task Management",
    integrations: [
      { name: "Atlassian Jira", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/jira.svg", connected: true },
      { name: "Linear", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/linear.svg", connected: false },
    ]
  },
  {
    title: "Code Repository",
    description: "Also the source of Users & Teams in your Engineering organization.",
    integrations: [
      { name: "GitHub", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/github.svg", connected: true },
      { name: "GitLab", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/gitlab.svg", connected: false },
    ]
  },
  {
    title: "CI/CD Pipeline",
    integrations: [
      { name: "GitHub Actions", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/githubactions.svg", connected: true },
      { name: "Harness.io", logo: "https://raw.githubusercontent.com/harness/developer-hub/refs/heads/main/static/img/hdh_fav_icon_grey.ico", connected: false },
      { name: "Jenkins", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/jenkins.svg", connected: false },
    ]
  },
  {
    title: "Artifact Registry",
    integrations: [
      { name: "JFrog", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/jfrog.svg", connected: false },
      { name: "AWS", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/amazonaws.svg", connected: true },
      { name: "GCP", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/googlecloud.svg", connected: false },
    ]
  },
  {
    title: "Regression Testing & Code Coverage",
    integrations: [
      { name: "SonarQube", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/sonarqube.svg", connected: true },
    ]
  },
  {
    title: "Security Testing",
    integrations: [
      { name: "Snyk", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/snyk.svg", connected: true },
      { name: "Semgrep.dev", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/semgrep.svg", connected: false },
    ]
  },
  {
    title: "Feature Flags",
    integrations: [
      { name: "LaunchDarkly", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/launchdarkly.svg", connected: false },
    ]
  },
  {
    title: "IT Service Management",
    integrations: [
      { name: "ServiceNow", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/servicenow.svg", connected: false },
    ]
  },
  {
    title: "Compute Runtime",
    integrations: [
      { name: "Kubernetes", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/kubernetes.svg", connected: true },
    ]
  },
  {
    title: "Cloud Platform",
    integrations: [
      { name: "AWS", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/amazonaws.svg", connected: true },
      { name: "GCP", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/googlecloud.svg", connected: false },
      { name: "Microsoft Azure", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/microsoftazure.svg", connected: false },
    ]
  },
  {
    title: "Data Analytics Platform",
    integrations: [
      { name: "Databricks", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/databricks.svg", connected: false },
      { name: "Snowflake", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/snowflake.svg", connected: true },
    ]
  },
  {
    title: "LLM Provider",
    integrations: [
      { name: "OpenAI", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/openai.svg", connected: true },
      { name: "Anthropic AI", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/anthropic.svg", connected: true },
    ]
  },
  {
    title: "Observability Platform",
    integrations: [
      { name: "Datadog", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/datadog.svg", connected: true },
    ]
  },
  {
    title: "Incident Response",
    integrations: [
      { name: "PagerDuty", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/pagerduty.svg", connected: false },
    ]
  },
  {
    title: "Infrastructure as Code",
    integrations: [
      { name: "Terraform", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/terraform.svg", connected: true },
      { name: "Pulumi", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/pulumi.svg", connected: false },
    ]
  },
  {
    title: "Secrets Management",
    integrations: [
      { name: "HashiCorp Vault", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/vault.svg", connected: false },
    ]
  },
  {
    title: "Customer Support",
    integrations: [
      { name: "Zendesk", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/zendesk.svg", connected: false },
      { name: "usepylon.com", logo: "https://usepylon.com/favicon.ico", connected: false },
    ]
  },
  {
    title: "Documentation",
    integrations: [
      { name: "Atlassian Confluence", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/confluence.svg", connected: true },
      { name: "Notion", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/notion.svg", connected: false },
    ]
  },
  {
    title: "Chat",
    integrations: [
      { name: "Slack", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/slack.svg", connected: true },
      { name: "Microsoft Teams", logo: "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/microsoftteams.svg", connected: false },
    ]
  },
];

const IntegrationsPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [selectedCategoryIdx, setSelectedCategoryIdx] = useState(0);

  const selectedCategory = integrationCategories[selectedCategoryIdx];
  const filteredIntegrations = selectedCategory.integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === "all" || 
                         (selectedFilter === "connected" && integration.connected) ||
                         (selectedFilter === "not-connected" && !integration.connected);
    return matchesSearch && matchesFilter;
  });

  const totalConnected = integrationCategories.reduce((acc, category) => 
    acc + category.integrations.filter(integration => integration.connected).length, 0
  );

  const totalIntegrations = integrationCategories.reduce((acc, category) => 
    acc + category.integrations.length, 0
  );

  return (
    <div className="min-h-screen bg-[var(--bg-primary)] text-[var(--text-primary)] p-8 font-['Manrope',sans-serif]">
      {/* Header - MATCHING MAIN LAYOUT STYLING */}
      <div className="mb-8">
        <h1 className="font-['Manrope',Helvetica] font-bold text-[var(--text-primary)] text-lg leading-[22.3px] mb-1">
          Account Settings &gt; Tool Integrations
        </h1>
        <p className="font-['Figtree',Helvetica] font-normal text-[var(--text-secondary)] text-sm leading-[19.6px] mb-6">
          Connect and manage your SDLC tools
        </p>
        
        {/* Header Stats */}
        <div className="flex items-center gap-8 mb-6">
          <div className={`border rounded-lg px-6 py-4 ${
            resolvedTheme === 'light'
              ? 'bg-[var(--bg-secondary)] border-indigo-400/40'
              : 'bg-[var(--bg-secondary)] border-cyan-400/40'
          }`}>
            <div className={`text-2xl font-bold ${
              resolvedTheme === 'light' ? 'text-indigo-600' : 'text-cyan-300'
            }`}>{totalConnected}</div>
            <div className="text-sm text-[var(--text-secondary)]">Connected</div>
          </div>
          <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-6 py-4">
            <div className="text-2xl font-bold text-[var(--text-primary)]">{totalIntegrations - totalConnected}</div>
            <div className="text-sm text-[var(--text-secondary)]">Not Connected</div>
          </div>
          <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-6 py-4">
            <div className="text-2xl font-bold text-[var(--text-primary)]">{totalIntegrations}</div>
            <div className="text-sm text-[var(--text-secondary)]">Total Tool Integrations</div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
            <input
              type="text"
              placeholder="Search ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
            />
          </div>
          <div className="flex items-center gap-2">
            <FilterIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-3 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
            >
              <option value="all">All Tool Integrations</option>
              <option value="connected">Connected</option>
              <option value="not-connected">Not Connected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Integration Categories with Vertical Selector */}
      <div className="flex gap-8">
        {/* Category Selector */}
        <div className="w-64 flex-shrink-0">
          {/* TOOL CATEGORY Heading */}
          <h3 className="font-bold text-[var(--text-primary)] text-lg mb-4 uppercase tracking-wide">TOOL CATEGORY</h3>
          
          <div className={`flex flex-col border rounded-lg py-2 ${
            resolvedTheme === 'light'
              ? 'bg-[var(--bg-secondary)] border-indigo-400/40'
              : 'bg-[var(--bg-secondary)] border-cyan-400/40'
          }`}>
            {integrationCategories.map((cat, idx) => (
              <button
                key={cat.title}
                className={`px-6 py-3 text-left font-semibold text-sm rounded-lg mb-1 transition-all
                  ${idx === selectedCategoryIdx
                    ? resolvedTheme === 'light'
                      ? 'bg-indigo-600/80 text-white border-l-4 border-indigo-600'
                      : 'bg-cyan-900/40 text-cyan-300 border-l-4 border-cyan-400'
                    : 'text-[var(--text-secondary)] hover:bg-[var(--bg-tertiary)]'
                  }`}
                onClick={() => setSelectedCategoryIdx(idx)}
              >
                {cat.title}
              </button>
            ))}
          </div>
        </div>
        {/* Integrations Tiles */}
        <div className="flex-1">
          <h2 className="font-bold text-[var(--text-primary)] text-lg mb-2 uppercase tracking-wide">
            {selectedCategory.title}
          </h2>
          {/* Category Description */}
          {selectedCategory.description && (
            <p className="text-[var(--text-secondary)] text-sm mb-4 font-['Figtree',sans-serif]">
              {selectedCategory.description}
            </p>
          )}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredIntegrations.map((integration) => (
              <Card
                key={integration.name}
                className={`bg-[var(--bg-secondary)] border transition-all cursor-pointer group hover:scale-105 ${
                  integration.connected
                    ? resolvedTheme === 'light'
                      ? 'border-indigo-400/60 hover:ring-2 hover:ring-indigo-300/80'
                      : 'border-cyan-400/60 hover:ring-2 hover:ring-cyan-300/80'
                    : resolvedTheme === 'light'
                      ? 'border-[var(--border-primary)] hover:border-indigo-400/40 hover:ring-2 hover:ring-indigo-300/40'
                      : 'border-[var(--border-primary)] hover:border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/40'
                }`}
              >
                <CardContent className="p-6 text-center">
                  <div className="relative mb-4">
                    <div className="w-16 h-16 mx-auto rounded-lg bg-white p-3 flex items-center justify-center">
                      <img
                        src={integration.logo}
                        alt={`${integration.name} logo`}
                        className="w-full h-full object-contain filter"
                        style={{ filter: 'brightness(0) saturate(100%)' }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          const currentSrc = target.src;

                          // Try fallback sources based on integration name
                          const integrationName = integration.name.toLowerCase();

                          // First fallback: try simple-icons if not already tried
                          if (!currentSrc.includes('simple-icons') && !currentSrc.includes('data:image')) {
                            if (integrationName.includes('github')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/github.svg";
                              return;
                            } else if (integrationName.includes('slack')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/slack.svg";
                              return;
                            } else if (integrationName.includes('jira')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/jira.svg";
                              return;
                            } else if (integrationName.includes('linear')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/linear.svg";
                              return;
                            } else if (integrationName.includes('openai')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/openai.svg";
                              return;
                            } else if (integrationName.includes('anthropic')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/anthropic.svg";
                              return;
                            } else if (integrationName.includes('aws')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/amazonaws.svg";
                              return;
                            } else if (integrationName.includes('terraform')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/terraform.svg";
                              return;
                            } else if (integrationName.includes('kubernetes')) {
                              target.src = "https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v11/icons/kubernetes.svg";
                              return;
                            }
                          }

                          // Final fallback: professional generic icon
                          target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='2' y='3' width='20' height='14' rx='2' ry='2'/%3E%3Cline x1='8' y1='21' x2='16' y2='21'/%3E%3Cline x1='12' y1='17' x2='12' y2='21'/%3E%3C/svg%3E";
                          target.style.filter = 'none';
                          target.style.opacity = '0.6';
                        }}
                      />
                    </div>
                    {integration.connected && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                  <h3 className="font-semibold text-[var(--text-primary)] text-sm mb-2">
                    {integration.name}
                  </h3>
                  <div className="flex items-center justify-center">
                    {integration.connected ? (
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${
                        resolvedTheme === 'light'
                          ? 'bg-green-600/80 text-white border-green-600'
                          : 'bg-green-500/20 text-green-400 border-green-500/30'
                      }`}>
                        Connected
                      </span>
                    ) : (
                      <button className="px-3 py-1 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-full text-xs font-medium transition-colors flex items-center gap-1">
                        <PlusIcon className="w-3 h-3" />
                        Connect
                      </button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          {filteredIntegrations.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-[var(--bg-secondary)] rounded-full flex items-center justify-center mx-auto mb-4">
                <SearchIcon className="w-8 h-8 text-[var(--text-tertiary)]" />
              </div>
              <h3 className="font-semibold text-[var(--text-primary)] text-lg mb-2">
                No integrations found
              </h3>
              <p className="font-normal text-[var(--text-secondary)] text-sm">
                Try adjusting your search terms or filters
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IntegrationsPage;