import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ChevronDownIcon, SearchIcon, FilterIcon, ZoomInIcon, ZoomOutIcon, RotateCcwIcon } from 'lucide-react';
import { Card, CardContent } from '../../components/ui/card';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';

interface GraphNode {
  id: string;
  label: string;
  type: 'domain' | 'system' | 'component' | 'repo' | 'agent' | 'pipeline' | 'testing' | 'artifact' | 'environment';
  x: number;
  y: number;
  level: number;
  parentId?: string;
  children?: string[];
  color: string;
  size: number;
  originalX: number; // Store original position
  originalY: number; // Store original position
}

interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  type: 'hierarchy' | 'flow' | 'dependency';
  color: string;
}

const teams = [
  'Web Frontend',
  'Web Backend', 
  'Shared Platform',
  'Mobile',
  'Data Engineering',
  'DevOps',
  'All Teams'
];

const nodeColors = {
  domain: '#7c3aed', // purple-600
  system: '#2563eb', // blue-600
  component: '#059669', // emerald-600
  repo: '#dc2626', // red-600
  agent: '#ea580c', // orange-600
  pipeline: '#0891b2', // cyan-600
  testing: '#7c2d12', // amber-800
  artifact: '#4338ca', // indigo-600
  environment: '#166534' // green-800
};

const createGraphData = (selectedTeam: string) => {
  const nodes: GraphNode[] = [
    // Domain level (leftmost)
    {
      id: 'domain-1',
      label: 'acme.com',
      type: 'domain',
      x: 100,
      y: 300,
      originalX: 100,
      originalY: 300,
      level: 0,
      color: nodeColors.domain,
      size: 60,
      children: ['system-1']
    },
    
    // System level
    {
      id: 'system-1',
      label: 'Online Orders',
      type: 'system',
      x: 300,
      y: 300,
      originalX: 300,
      originalY: 300,
      level: 1,
      parentId: 'domain-1',
      color: nodeColors.system,
      size: 50,
      children: ['component-1']
    },
    
    // Component level
    {
      id: 'component-1',
      label: 'Catalog Service',
      type: 'component',
      x: 500,
      y: 300,
      originalX: 500,
      originalY: 300,
      level: 2,
      parentId: 'system-1',
      color: nodeColors.component,
      size: 45,
      children: ['repo-1']
    },
    
    // Code Repository
    {
      id: 'repo-1',
      label: 'GitHub',
      type: 'repo',
      x: 700,
      y: 300,
      originalX: 700,
      originalY: 300,
      level: 3,
      parentId: 'component-1',
      color: nodeColors.repo,
      size: 40,
      children: ['agent-1', 'agent-2', 'agent-3', 'pipeline-1']
    },
    
    // AI Agents (branching from GitHub) - MUCH BETTER SPACING
    {
      id: 'agent-1',
      label: 'Cursor',
      type: 'agent',
      x: 900,
      y: 80,
      originalX: 900,
      originalY: 80,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.agent,
      size: 35
    },
    {
      id: 'agent-2',
      label: 'Graphite',
      type: 'agent',
      x: 900,
      y: 180,
      originalX: 900,
      originalY: 180,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.agent,
      size: 35
    },
    {
      id: 'agent-3',
      label: 'AccelOS',
      type: 'agent',
      x: 900,
      y: 280,
      originalX: 900,
      originalY: 280,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.agent,
      size: 35
    },
    
    // CI/CD Pipeline (main branch from GitHub) - MOVED DOWN FOR BETTER SPACING
    {
      id: 'pipeline-1',
      label: 'GitHub Actions',
      type: 'pipeline',
      x: 900,
      y: 400,
      originalX: 900,
      originalY: 400,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.pipeline,
      size: 40,
      children: ['testing-1', 'testing-2', 'testing-3']
    },
    
    // Testing nodes (branching from CI/CD) - IMPROVED VERTICAL SPACING
    {
      id: 'testing-1',
      label: 'Snyk',
      type: 'testing',
      x: 1100,
      y: 300,
      originalX: 1100,
      originalY: 300,
      level: 5,
      parentId: 'pipeline-1',
      color: nodeColors.testing,
      size: 30
    },
    {
      id: 'testing-2',
      label: 'Prometheus, Grafana',
      type: 'testing',
      x: 1100,
      y: 400,
      originalX: 1100,
      originalY: 400,
      level: 5,
      parentId: 'pipeline-1',
      color: nodeColors.testing,
      size: 30
    },
    {
      id: 'testing-3',
      label: 'LaunchDarkly',
      type: 'testing',
      x: 1100,
      y: 500,
      originalX: 1100,
      originalY: 500,
      level: 5,
      parentId: 'pipeline-1',
      color: nodeColors.testing,
      size: 30
    },
    
    // Test Artifacts and Environments (branching from GitHub) - IMPROVED SPACING
    {
      id: 'artifact-test',
      label: 'v2 in aws-ecr-test',
      type: 'artifact',
      x: 900,
      y: 520,
      originalX: 900,
      originalY: 520,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.artifact,
      size: 35,
      children: ['env-test']
    },
    {
      id: 'env-test',
      label: 'aws-eks-test',
      type: 'environment',
      x: 1100,
      y: 620,
      originalX: 1100,
      originalY: 620,
      level: 5,
      parentId: 'artifact-test',
      color: nodeColors.environment,
      size: 35
    },
    
    // Production Artifacts and Environments (branching from GitHub) - IMPROVED SPACING
    {
      id: 'artifact-prod',
      label: 'v1 in aws-ecr-prod',
      type: 'artifact',
      x: 900,
      y: 640,
      originalX: 900,
      originalY: 640,
      level: 4,
      parentId: 'repo-1',
      color: nodeColors.artifact,
      size: 35,
      children: ['env-prod']
    },
    {
      id: 'env-prod',
      label: 'aws-eks-prod',
      type: 'environment',
      x: 1100,
      y: 740,
      originalX: 1100,
      originalY: 740,
      level: 5,
      parentId: 'artifact-prod',
      color: nodeColors.environment,
      size: 35
    }
  ];

  const edges: GraphEdge[] = [
    // Main hierarchy edges (left to right)
    { id: 'edge-1', source: 'domain-1', target: 'system-1', type: 'hierarchy', color: '#64748b' },
    { id: 'edge-2', source: 'system-1', target: 'component-1', type: 'hierarchy', color: '#64748b' },
    { id: 'edge-3', source: 'component-1', target: 'repo-1', type: 'hierarchy', color: '#64748b' },
    
    // Repository connections (branching right) - INCLUDING ACCELOS
    { id: 'edge-4', source: 'repo-1', target: 'agent-1', type: 'dependency', color: '#f59e0b' },
    { id: 'edge-5', source: 'repo-1', target: 'agent-2', type: 'dependency', color: '#f59e0b' },
    { id: 'edge-6', source: 'repo-1', target: 'agent-3', type: 'dependency', color: '#f59e0b' },
    { id: 'edge-7', source: 'repo-1', target: 'pipeline-1', type: 'dependency', color: '#06b6d4' },
    
    // Pipeline to testing (branching right)
    { id: 'edge-8', source: 'pipeline-1', target: 'testing-1', type: 'flow', color: '#10b981' },
    { id: 'edge-9', source: 'pipeline-1', target: 'testing-2', type: 'flow', color: '#10b981' },
    { id: 'edge-10', source: 'pipeline-1', target: 'testing-3', type: 'flow', color: '#10b981' },
    
    // Artifact flows (branching right)
    { id: 'edge-11', source: 'repo-1', target: 'artifact-test', type: 'flow', color: '#8b5cf6' },
    { id: 'edge-12', source: 'artifact-test', target: 'env-test', type: 'flow', color: '#8b5cf6' },
    { id: 'edge-13', source: 'repo-1', target: 'artifact-prod', type: 'flow', color: '#8b5cf6' },
    { id: 'edge-14', source: 'artifact-prod', target: 'env-prod', type: 'flow', color: '#8b5cf6' }
  ];

  return { nodes, edges };
};

const KnowledgeGraphPage: React.FC = () => {
  const navigate = useNavigate();
  const { resolvedTheme } = useTheme();
  const [selectedTeam, setSelectedTeam] = useState(teams[0]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isDraggingNode, setIsDraggingNode] = useState(false);
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);
  const [nodes, setNodes] = useState<GraphNode[]>([]);
  
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize nodes when team changes
  useEffect(() => {
    const { nodes: initialNodes } = createGraphData(selectedTeam);
    setNodes(initialNodes);
  }, [selectedTeam]);

  const { edges } = createGraphData(selectedTeam);

  const filteredNodes = nodes.filter(node => {
    const matchesSearch = node.label.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || node.type === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const filteredEdges = edges.filter(edge => {
    const sourceVisible = filteredNodes.some(n => n.id === edge.source);
    const targetVisible = filteredNodes.some(n => n.id === edge.target);
    return sourceVisible && targetVisible;
  });

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.3));
  
  // FIXED RESET FUNCTION - Reset nodes to original positions, zoom, and pan
  const handleResetView = useCallback(() => {
    console.log('🔄 Resetting graph view and node positions...');
    
    // Reset zoom and pan
    setZoom(1);
    setPan({ x: 0, y: 0 });
    
    // Reset all nodes to their original positions
    setNodes(prev => prev.map(node => ({
      ...node,
      x: node.originalX,
      y: node.originalY
    })));
    
    // Clear any selections
    setSelectedNode(null);
    
    console.log('✅ Graph reset complete!');
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === svgRef.current && !isDraggingNode) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
    }
  };

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && !isDraggingNode) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    } else if (isDraggingNode && draggedNodeId && svgRef.current) {
      // Get SVG coordinates
      const svgRect = svgRef.current.getBoundingClientRect();
      const svgX = (e.clientX - svgRect.left - pan.x) / zoom;
      const svgY = (e.clientY - svgRect.top - pan.y) / zoom;

      // Update node position
      setNodes(prev => prev.map(node => 
        node.id === draggedNodeId 
          ? { ...node, x: svgX, y: svgY }
          : node
      ));
    }
  }, [isDragging, isDraggingNode, draggedNodeId, dragStart, pan, zoom]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsDraggingNode(false);
    setDraggedNodeId(null);
  }, []);

  const handleNodeMouseDown = useCallback((e: React.MouseEvent, nodeId: string) => {
    e.stopPropagation();
    setIsDraggingNode(true);
    setDraggedNodeId(nodeId);
    setSelectedNode(nodeId);
  }, []);

  const handleNodeClick = useCallback((nodeId: string) => {
    if (!isDraggingNode) {
      setSelectedNode(selectedNode === nodeId ? null : nodeId);
    }
  }, [selectedNode, isDraggingNode]);

  const generateStraightPath = (x1: number, y1: number, x2: number, y2: number) => {
    return `M ${x1} ${y1} L ${x2} ${y2}`;
  };

  const generateCurvedPath = (x1: number, y1: number, x2: number, y2: number) => {
    const dx = x2 - x1;
    const dy = y2 - y1;
    
    // Control points for smooth curves
    const cp1x = x1 + dx * 0.5;
    const cp1y = y1;
    const cp2x = x2 - dx * 0.5;
    const cp2y = y2;
    
    return `M ${x1} ${y1} C ${cp1x} ${cp1y} ${cp2x} ${cp2y} ${x2} ${y2}`;
  };

  const nodeTypeLabels = {
    domain: 'Domain',
    system: 'System', 
    component: 'Component',
    repo: 'Code Repository',
    agent: 'AI Agent',
    pipeline: 'CI/CD Pipeline',
    testing: 'Testing',
    artifact: 'Artifact',
    environment: 'Environment'
  };

  // FIXED: Proper team selection handling with navigation
  const handleTeamSelection = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    console.log('🎯 Team selection changed to:', value);
    
    if (value === '+ New Team') {
      console.log('🚀 Navigating to New Session with Knowledge Graph Agent...');
      navigate('/chat-sessions/new?agent=Knowledge%20Graph%20Agent');
    } else if (teams.includes(value)) {
      console.log('📋 Setting selected team to:', value);
      setSelectedTeam(value);
    }
  };

  return (
    <div className="h-full bg-[var(--bg-primary)] text-[var(--text-primary)] flex font-['Manrope',sans-serif]">
      {/* Sidebar - NO GAP */}
      <div className="w-80 bg-[var(--bg-secondary)] p-6 overflow-y-auto">
        {/* Team Selection */}
        <div className="mb-6">
          <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Select a Team</h3>
          <div className="relative">
            <select
              value={selectedTeam}
              onChange={handleTeamSelection}
              className="w-full bg-[var(--bg-primary)] border border-cyan-400/40 rounded-lg px-4 py-3 text-[var(--text-primary)] font-semibold focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 hover:border-cyan-300 appearance-none transition-all cursor-pointer"
            >
              {teams.map((team) => (
                <option key={team} value={team} className="bg-[var(--bg-primary)] text-[var(--text-primary)]">
                  {team}
                </option>
              ))}
              <option value="+ New Team" className="bg-[var(--bg-primary)] text-cyan-300 font-semibold">
                + New Team
              </option>
            </select>
            <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--text-tertiary)] pointer-events-none" />
          </div>
          
          {/* Helper text for + New Team */}
          <div className="mt-2 text-xs text-cyan-300 font-['Figtree',sans-serif]">
            Select "+ New Team" to start a chat session with the Knowledge Graph Agent
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6">
          <div className="relative mb-4">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
            <input
              type="text"
              placeholder="Search nodes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400"
            />
          </div>
          
          <div className="flex items-center gap-2 mb-4">
            <FilterIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="flex-1 px-3 py-2 bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400"
            >
              <option value="all">All Types</option>
              {Object.entries(nodeTypeLabels).map(([type, label]) => (
                <option key={type} value={type}>{label}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Graph Controls */}
        <div className="mb-6">
          <h4 className="text-[var(--text-primary)] font-semibold text-sm mb-3">Graph Controls</h4>
          <div className="flex gap-2">
            <button
              onClick={handleZoomIn}
              className="flex-1 p-2 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all flex items-center justify-center gap-2"
            >
              <ZoomInIcon className="w-4 h-4" />
              <span className="text-xs">Zoom In</span>
            </button>
            <button
              onClick={handleZoomOut}
              className="flex-1 p-2 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all flex items-center justify-center gap-2"
            >
              <ZoomOutIcon className="w-4 h-4" />
              <span className="text-xs">Zoom Out</span>
            </button>
            <button
              onClick={handleResetView}
              className="flex-1 p-2 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all flex items-center justify-center gap-2 hover:bg-cyan-900/20"
              title="Reset graph to original layout"
            >
              <RotateCcwIcon className="w-4 h-4" />
              <span className="text-xs">Reset</span>
            </button>
          </div>
        </div>

        {/* Legend - 2 COLUMN LAYOUT */}
        <div className="mb-6">
          <h4 className="text-[var(--text-primary)] font-semibold text-sm mb-3">Node Types</h4>
          <div className="grid grid-cols-2 gap-x-3 gap-y-2">
            {Object.entries(nodeTypeLabels).map(([type, label]) => (
              <div key={type} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full border border-white/20 flex-shrink-0"
                  style={{ backgroundColor: nodeColors[type as keyof typeof nodeColors] }}
                ></div>
                <span className="text-[var(--text-secondary)] text-xs font-['Figtree',sans-serif] truncate">{label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Selected Node Info */}
        {selectedNode && (
          <Card className={`bg-[var(--bg-primary)] border ${
            resolvedTheme === 'light' ? 'border-indigo-400/40' : 'border-cyan-400/40'
          }`}>
            <CardContent className="p-4">
              <h4 className="text-[var(--text-primary)] font-semibold text-sm mb-2">Selected Node</h4>
              {(() => {
                const node = nodes.find(n => n.id === selectedNode);
                if (!node) return null;
                return (
                  <div>
                    <div className={`font-medium ${
                      resolvedTheme === 'light' ? 'text-indigo-600' : 'text-cyan-300'
                    }`}>{node.label}</div>
                    <div className="text-[var(--text-tertiary)] text-xs mt-1">
                      {nodeTypeLabels[node.type]} • Level {node.level}
                    </div>
                    <div className="text-[var(--text-tertiary)] text-xs mt-1">
                      Position: ({Math.round(node.x)}, {Math.round(node.y)})
                    </div>
                    <div className="text-[var(--text-tertiary)] text-xs mt-1">
                      Original: ({node.originalX}, {node.originalY})
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Main Graph Area - NO GAP */}
      <div className="flex-1 relative overflow-hidden bg-[var(--bg-primary)]">
        {/* Graph Header */}
        <div className="absolute top-6 left-6 z-30">
          <div className={`bg-[var(--bg-secondary)] border rounded-lg px-4 py-2 ${
            resolvedTheme === 'light' ? 'border-indigo-400/40' : 'border-cyan-400/40'
          }`}>
            <h2 className="text-[var(--text-primary)] font-bold text-lg">
              {selectedTeam} Knowledge Graph
            </h2>
            <p className="text-[var(--text-secondary)] text-sm">
              {filteredNodes.length} nodes • {filteredEdges.length} connections
            </p>
          </div>
        </div>

        {/* Graph Canvas */}
        <div
          ref={containerRef}
          className={`w-full h-full relative ${isDraggingNode ? 'cursor-grabbing' : (isDragging ? 'cursor-grabbing' : 'cursor-grab')}`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <svg
            ref={svgRef}
            className="w-full h-full"
            style={{
              transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
              transformOrigin: 'center center'
            }}
          >
            <defs>
              <marker
                id="arrowhead"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon points="0 0, 12 4, 0 8" fill="#64748b" />
              </marker>
              <marker
                id="arrowhead-hierarchy"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon points="0 0, 12 4, 0 8" fill="#22d3ee" />
              </marker>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge> 
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Render Edges */}
            {filteredEdges.map((edge) => {
              const sourceNode = filteredNodes.find(n => n.id === edge.source);
              const targetNode = filteredNodes.find(n => n.id === edge.target);
              
              if (!sourceNode || !targetNode) return null;

              // Calculate connection points (right side of source, left side of target)
              const sourceX = sourceNode.x + sourceNode.size/2;
              const sourceY = sourceNode.y;
              const targetX = targetNode.x - targetNode.size/2;
              const targetY = targetNode.y;

              const path = edge.type === 'hierarchy' 
                ? generateStraightPath(sourceX, sourceY, targetX, targetY)
                : generateCurvedPath(sourceX, sourceY, targetX, targetY);

              const markerEnd = edge.type === 'hierarchy' ? 'url(#arrowhead-hierarchy)' : 'url(#arrowhead)';
              const strokeColor = edge.type === 'hierarchy' ? '#22d3ee' : edge.color;

              return (
                <g key={edge.id}>
                  {/* Glow effect */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={edge.type === 'hierarchy' ? 6 : 4}
                    fill="none"
                    opacity={0.3}
                    filter="blur(2px)"
                  />
                  
                  {/* Main path - ALL ARROWS NOW SOLID */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={edge.type === 'hierarchy' ? 3 : 2}
                    fill="none"
                    markerEnd={markerEnd}
                    opacity={0.9}
                  />
                  
                  {edge.label && (
                    <text
                      x={(sourceX + targetX) / 2}
                      y={(sourceY + targetY) / 2 - 10}
                      fill={strokeColor}
                      fontSize="10"
                      textAnchor="middle"
                      className="font-['Figtree',sans-serif] select-none"
                    >
                      {edge.label}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Render Nodes */}
            {filteredNodes.map((node) => (
              <g key={node.id}>
                {/* Main node circle - removed glow/shadow effects */}
                <circle
                  cx={node.x}
                  cy={node.y}
                  r={node.size / 2}
                  fill={node.color}
                  stroke={selectedNode === node.id ? (resolvedTheme === 'light' ? '#4f46e5' : '#22d3ee') : '#ffffff'}
                  strokeWidth={selectedNode === node.id ? 3 : 1}
                  className={`transition-all duration-200 ${
                    resolvedTheme === 'light' ? 'hover:stroke-indigo-400' : 'hover:stroke-cyan-300'
                  } ${isDraggingNode && draggedNodeId === node.id ? 'cursor-grabbing' : 'cursor-grab'}`}
                  onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
                  onClick={() => handleNodeClick(node.id)}
                />
                
                {/* Node label - IMPROVED SPACING */}
                <text
                  x={node.x}
                  y={node.y + node.size/2 + 25}
                  fill={selectedNode === node.id ? (resolvedTheme === 'light' ? '#4f46e5' : '#22d3ee') : (resolvedTheme === 'light' ? '#1f2937' : '#ffffff')}
                  fontSize="12"
                  textAnchor="middle"
                  className="font-['Figtree',sans-serif] select-none pointer-events-none"
                >
                  {node.label}
                </text>
                
                {/* Node type indicator - IMPROVED SPACING */}
                <text
                  x={node.x}
                  y={node.y + node.size/2 + 42}
                  fill="#9ea4aa"
                  fontSize="10"
                  textAnchor="middle"
                  className="font-['Figtree',sans-serif] select-none pointer-events-none"
                >
                  {nodeTypeLabels[node.type]}
                </text>
              </g>
            ))}
          </svg>
        </div>

        {/* Zoom indicator */}
        <div className="absolute bottom-6 right-6 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-3 py-2">
          <span className="text-[var(--text-secondary)] text-sm font-['Figtree',sans-serif]">
            Zoom: {Math.round(zoom * 100)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeGraphPage;