import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  PlayIcon,
  SaveIcon,
  SettingsIcon,
  TrashIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  LinkIcon
} from 'lucide-react';
import { Card, CardContent } from '../../components/ui/card';
import { useTheme } from '../../contexts/ThemeContext';

interface FlowNode {
  id: string;
  type: 'sdlc-tool' | 'agent' | 'chat-input' | 'chat-output' | 'note';
  position: { x: number; y: number };
  data: {
    title: string;
    description: string;
    config: Record<string, any>;
    collapsed?: boolean;
  };
}

interface Connection {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  style?: {
    color?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
    arrowStyle?: 'default' | 'large' | 'filled';
  };
  label?: string;
  selected?: boolean;
}

const nodeTypes = {
  agent: {
    title: 'Agent',
    description: 'Define the agent\'s instructions',
    color: 'bg-[#7678ed]',
    icon: '🤖'
  },
  'sdlc-tool': {
    title: 'SDLC Tool To Be Configured',
    description: 'Configure and connect SDLC tools from your integrations',
    color: 'bg-[#7678ed]',
    icon: '🔧'
  },
  'chat-input': {
    title: 'User Input',
    description: 'Receive input from user',
    color: 'bg-[#9ea4aa]',
    icon: '💬'
  },
  'chat-output': {
    title: 'User Output',
    description: 'Send output to user',
    color: 'bg-cyan-600',
    icon: '📤'
  },
  note: {
    title: 'Note',
    description: 'Documentation and notes',
    color: 'bg-amber-600',
    icon: '📝'
  }
};

// Get connected integrations from the integrations data
const getConnectedIntegrations = () => {
  const integrationCategories = [
    {
      title: "AI Coding Agent",
      integrations: [
        { name: "Cursor", connected: true },
        { name: "GitHub Copilot", connected: true },
      ]
    },
    {
      title: "AI Code Review Agent",
      integrations: [
        { name: "CodeRabbit", connected: true },
      ]
    },
    {
      title: "Task Management",
      integrations: [
        { name: "Atlassian Jira", connected: true },
      ]
    },
    {
      title: "Code Repository",
      integrations: [
        { name: "GitHub", connected: true },
      ]
    },
    {
      title: "CI/CD Pipeline",
      integrations: [
        { name: "GitHub Actions", connected: true },
      ]
    },
    {
      title: "Artifact Registry",
      integrations: [
        { name: "AWS", connected: true },
      ]
    },
    {
      title: "Regression Testing & Code Coverage",
      integrations: [
        { name: "SonarQube", connected: true },
      ]
    },
    {
      title: "Security Testing",
      integrations: [
        { name: "Snyk", connected: true },
      ]
    },
    {
      title: "Compute Runtime",
      integrations: [
        { name: "Kubernetes", connected: true },
      ]
    },
    {
      title: "Cloud Platform",
      integrations: [
        { name: "AWS", connected: true },
      ]
    },
    {
      title: "Data Analytics Platform",
      integrations: [
        { name: "Snowflake", connected: true },
      ]
    },
    {
      title: "LLM Provider",
      integrations: [
        { name: "OpenAI", connected: true },
        { name: "Anthropic AI", connected: true },
      ]
    },
    {
      title: "Observability Platform",
      integrations: [
        { name: "Datadog", connected: true },
      ]
    },
    {
      title: "Infrastructure as Code",
      integrations: [
        { name: "Terraform", connected: true },
      ]
    },
    {
      title: "Documentation",
      integrations: [
        { name: "Atlassian Confluence", connected: true },
      ]
    },
    {
      title: "Chat",
      integrations: [
        { name: "Slack", connected: true },
      ]
    },
  ];

  // Flatten and filter connected integrations
  const connectedIntegrations: Array<{name: string, category: string}> = [];
  integrationCategories.forEach(category => {
    category.integrations.forEach(integration => {
      if (integration.connected) {
        connectedIntegrations.push({
          name: integration.name,
          category: category.title
        });
      }
    });
  });

  return connectedIntegrations;
};

const ToolConfigurationAgent: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [nodes, setNodes] = useState<FlowNode[]>([
    {
      id: 'sdlc-tool-1',
      type: 'sdlc-tool',
      position: { x: 100, y: 300 },
      data: {
        title: 'SDLC Tool To Be Configured',
        description: 'Configure and connect SDLC tools from your integrations',
        collapsed: false,
        config: {
          selectedTool: '',
          toolCategory: '',
          configuration: {}
        }
      }
    },
    {
      id: 'agent-1',
      type: 'agent',
      position: { x: 500, y: 300 },
      data: {
        title: 'Agent',
        description: 'Define the agent\'s instructions',
        collapsed: false,
        config: {
          modelProvider: 'OpenAI',
          modelName: 'gpt-4o',
          apiKey: '',
          instructions: 'You are a helpful assistant that can...',
          tools: [],
          input: '',
          response: ''
        }
      }
    },
    {
      id: 'chat-input-1',
      type: 'chat-input',
      position: { x: 100, y: 600 },
      data: {
        title: 'User Input',
        description: 'Receive input from user',
        collapsed: false,
        config: {}
      }
    },
    {
      id: 'chat-output-1',
      type: 'chat-output',
      position: { x: 900, y: 300 },
      data: {
        title: 'User Output',
        description: 'Send output to user',
        collapsed: false,
        config: {}
      }
    }
  ]);

  const [connections, setConnections] = useState<Connection[]>([
    {
      id: 'conn-1',
      source: 'sdlc-tool-1',
      target: 'agent-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#22d3ee', // cyan-400 - consistent color
        strokeWidth: 3,
        arrowStyle: 'default'
      },
      label: 'Tools'
    },
    {
      id: 'conn-2',
      source: 'agent-1',
      target: 'chat-output-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#22d3ee', // cyan-400 - consistent color
        strokeWidth: 3,
        arrowStyle: 'default'
      }
    },
    {
      id: 'conn-3',
      source: 'chat-input-1',
      target: 'agent-1',
      sourceHandle: 'output',
      targetHandle: 'input',
      style: {
        color: '#22d3ee', // cyan-400 - consistent color
        strokeWidth: 3,
        arrowStyle: 'default'
      },
      label: 'Input'
    }
  ]);

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<{ nodeId: string; handle: string } | null>(null);
  const [tempConnection, setTempConnection] = useState<{ x: number; y: number } | null>(null);
  
  const canvasRef = useRef<HTMLDivElement>(null);



  // Get connected integrations
  const connectedIntegrations = getConnectedIntegrations();

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.key === 'Delete' || e.key === 'Backspace') && selectedConnection) {
        e.preventDefault();
        deleteSelectedConnection();
      }
      if ((e.key === 'Delete' || e.key === 'Backspace') && selectedNode) {
        e.preventDefault();
        deleteNode(selectedNode);
      }
      if (e.key === 'Escape') {
        setSelectedConnection(null);
        setSelectedNode(null);
        if (isConnecting) {
          setIsConnecting(false);
          setConnectionStart(null);
          setTempConnection(null);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedConnection, selectedNode, isConnecting]);

  const handleNodeMouseDown = useCallback((e: React.MouseEvent, nodeId: string) => {
    // Only start dragging if not clicking on interactive elements
    const target = e.target as HTMLElement;
    const isInteractiveElement = target.tagName === 'BUTTON' ||
                                target.tagName === 'INPUT' ||
                                target.tagName === 'SELECT' ||
                                target.tagName === 'TEXTAREA' ||
                                target.closest('button'); // Check if clicked element is inside a button

    if (isInteractiveElement) {
      console.log(`🚫 Skipping drag - clicked on interactive element:`, target.tagName);
      return;
    }

    // If we're in connecting mode, end the connection
    if (isConnecting && connectionStart && connectionStart.nodeId !== nodeId) {
      endConnection(nodeId, 'input');
      return;
    }

    e.preventDefault();
    setSelectedNode(nodeId);
    setIsDragging(true);

    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      const rect = e.currentTarget.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  }, [nodes, isConnecting, connectionStart]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && selectedNode && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      const newX = e.clientX - canvasRect.left - dragOffset.x;
      const newY = e.clientY - canvasRect.top - dragOffset.y;

      setNodes(prev => prev.map(node => 
        node.id === selectedNode 
          ? { ...node, position: { x: Math.max(0, newX), y: Math.max(0, newY) } }
          : node
      ));
    }

    if (isConnecting && canvasRef.current) {
      const canvasRect = canvasRef.current.getBoundingClientRect();
      setTempConnection({
        x: e.clientX - canvasRect.left,
        y: e.clientY - canvasRect.top
      });
    }
  }, [isDragging, selectedNode, dragOffset, isConnecting]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  }, []);

  const addNode = (type: keyof typeof nodeTypes) => {
    const newNode: FlowNode = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: 200, y: 200 },
      data: {
        title: nodeTypes[type].title,
        description: nodeTypes[type].description,
        collapsed: false,
        config: type === 'sdlc-tool' ? {
          selectedTool: '',
          toolCategory: '',
          configuration: {}
        } : {}
      }
    };
    setNodes(prev => [...prev, newNode]);
  };

  // FIXED DELETE FUNCTION - IMMEDIATE DELETE
  const deleteNode = useCallback((nodeId: string) => {
    console.log(`🗑️ Attempting to delete node: ${nodeId}`);
    
    const nodeToDelete = nodes.find(n => n.id === nodeId);
    if (!nodeToDelete) {
      console.log(`❌ Node ${nodeId} not found`);
      return;
    }

    // Remove the node immediately
    setNodes(prev => {
      const newNodes = prev.filter(n => n.id !== nodeId);
      console.log(`✅ Node deleted. Remaining nodes: ${newNodes.length}`);
      return newNodes;
    });
    
    // Remove all connections involving this node
    setConnections(prev => {
      const newConnections = prev.filter(c => c.source !== nodeId && c.target !== nodeId);
      console.log(`🔗 Connections cleaned up. Remaining connections: ${newConnections.length}`);
      return newConnections;
    });
    
    // Clear selection if this node was selected
    if (selectedNode === nodeId) {
      setSelectedNode(null);
    }

    // Clear any selected connections that were deleted
    const remainingConnections = connections.filter(c => c.source !== nodeId && c.target !== nodeId);
    if (selectedConnection && !remainingConnections.find(c => c.id === selectedConnection)) {
      setSelectedConnection(null);
    }

    console.log(`🎉 Successfully deleted node: ${nodeToDelete.data.title} (${nodeId})`);
  }, [nodes, connections, selectedNode, selectedConnection]);

  const toggleNodeCollapse = (nodeId: string) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { ...node, data: { ...node.data, collapsed: !node.data.collapsed } }
        : node
    ));
  };

  // COMPLETELY SIMPLIFIED: Direct config update
  const updateNodeConfig = useCallback((nodeId: string, configKey: string, value: any) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId 
        ? { 
            ...node, 
            data: { 
              ...node.data, 
              config: { 
                ...node.data.config, 
                [configKey]: value 
              } 
            } 
          }
        : node
    ));
  }, []);

  // SIMPLIFIED CONNECTION HANDLING
  const startConnection = (nodeId: string, handle: string) => {
    console.log('🔗 Starting connection from:', nodeId, handle);
    setIsConnecting(true);
    setConnectionStart({ nodeId, handle });
  };

  const endConnection = (nodeId: string, handle: string) => {
    console.log('🎯 Ending connection at:', nodeId, handle);

    if (connectionStart && connectionStart.nodeId !== nodeId) {
      const newConnection: Connection = {
        id: `conn-${Date.now()}`,
        source: connectionStart.nodeId,
        target: nodeId,
        sourceHandle: connectionStart.handle,
        targetHandle: handle,
        style: {
          color: '#22d3ee', // cyan-400 - bright cyan for new connections
          strokeWidth: 3, // Consistent with other connections
          arrowStyle: 'default',
          strokeDasharray: '8,4' // Make new connections dotted initially
        }
      };
      setConnections(prev => [...prev, newConnection]);
      setSelectedConnection(newConnection.id);
    }

    // Keep connection state active to show the dotted line persists
    // Don't reset immediately - let user click elsewhere to finish
  };

  const handleCanvasClick = useCallback(() => {
    if (isConnecting) {
      // Reset connection state when clicking on canvas
      setIsConnecting(false);
      setConnectionStart(null);
      setTempConnection(null);
    }
    setSelectedConnection(null);
    setSelectedNode(null);
  }, [isConnecting]);

  const generateCurvedPath = (sourceX: number, sourceY: number, targetX: number, targetY: number) => {
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    const curveStrength = Math.min(distance * 0.4, 150);
    const isForward = dx > 0;

    let cp1x, cp1y, cp2x, cp2y;

    if (isForward) {
      cp1x = sourceX + curveStrength;
      cp1y = sourceY;
      cp2x = targetX - curveStrength;
      cp2y = targetY;
    } else {
      const verticalOffset = Math.abs(dy) > 50 ? 0 : 80;
      cp1x = sourceX + curveStrength * 0.7;
      cp1y = sourceY + (dy > 0 ? -verticalOffset : verticalOffset);
      cp2x = targetX - curveStrength * 0.7;
      cp2y = targetY + (dy > 0 ? verticalOffset : -verticalOffset);
    }

    return `M ${sourceX} ${sourceY} C ${cp1x} ${cp1y} ${cp2x} ${cp2y} ${targetX} ${targetY}`;
  };

  const deleteSelectedConnection = () => {
    if (selectedConnection) {
      setConnections(prev => prev.filter(c => c.id !== selectedConnection));
      setSelectedConnection(null);
      console.log(`🗑️ Deleted connection: ${selectedConnection}`);
    }
  };

  const handleConnectionClick = (connectionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedConnection(connectionId);
    setSelectedNode(null);

    // Convert dotted connection to solid when clicked
    const connection = connections.find(c => c.id === connectionId);
    if (connection?.style?.strokeDasharray) {
      setConnections(prev => prev.map(c =>
        c.id === connectionId
          ? { ...c, style: { ...c.style, strokeDasharray: undefined } }
          : c
      ));
    }
  };

  const getNodeConnectionPoint = (node: FlowNode, handle: 'input' | 'output') => {
    const nodeWidth = node.type === 'note' ? 320 : 256;
    const nodeHeight = 120; // More accurate height estimate

    if (handle === 'output') {
      // Output connection point (right edge of node)
      return {
        x: node.position.x + nodeWidth,
        y: node.position.y + nodeHeight / 2 // Center vertically
      };
    } else {
      // Input connection point (left edge of node)
      return {
        x: node.position.x,
        y: node.position.y + nodeHeight / 2 // Center vertically
      };
    }
  };

  const NodeComponent: React.FC<{ node: FlowNode }> = ({ node }) => {
    const nodeType = nodeTypes[node.type];
    const isSelected = selectedNode === node.id;



    // COMPLETELY SIMPLIFIED: Basic dropdown with no complex state
    const handleToolSelection = useCallback((selectedValue: string) => {
      console.log(`📋 Tool selected for node ${node.id}: ${selectedValue}`);
      
      if (selectedValue) {
        const selectedIntegration = connectedIntegrations.find(int => int.name === selectedValue);
        
        // Update both tool and category immediately
        updateNodeConfig(node.id, 'selectedTool', selectedValue);
        if (selectedIntegration) {
          updateNodeConfig(node.id, 'toolCategory', selectedIntegration.category);
        }
        
        console.log(`✅ Integration configured: ${selectedValue} (${selectedIntegration?.category})`);
      } else {
        // Clear selection
        updateNodeConfig(node.id, 'selectedTool', '');
        updateNodeConfig(node.id, 'toolCategory', '');
      }
    }, [node.id, connectedIntegrations, updateNodeConfig]);

    return (
      <div
        className={`absolute cursor-move select-none ${isSelected ? 'z-20' : 'z-10'}`}
        style={{ 
          left: node.position.x, 
          top: node.position.y,
          transform: isDragging && isSelected ? 'scale(1.05)' : 'scale(1)',
          transition: isDragging && isSelected ? 'none' : 'transform 0.2s ease'
        }}
        onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
        onDoubleClick={(e) => {
          e.stopPropagation();
          if (node.type !== 'note') {
            startConnection(node.id, 'output');
          }
        }}
      >
        <Card className={`${node.type === 'note' ? 'w-80' : 'w-64'} bg-[var(--bg-primary)] border transition-all font-['Manrope',sans-serif] ${
          isSelected ? 'border-cyan-400 ring-2 ring-cyan-300/80' : 'border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80'
        }`}>
          <CardContent className="p-4">
            {/* Node Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className={`w-6 h-6 rounded ${nodeType.color} flex items-center justify-center text-white text-sm`}>
                  {nodeType.icon}
                </div>
                <span className="text-[var(--text-primary)] font-semibold text-sm">{node.data.title}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleNodeCollapse(node.id);
                  }}
                  className="p-1 hover:bg-[var(--bg-secondary)] rounded transition-colors"
                >
                  {node.data.collapsed ? (
                    <ChevronDownIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                  ) : (
                    <ChevronUpIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                  )}
                </button>
              </div>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log(`🗑️ TRASH BUTTON CLICKED - Node ID: ${node.id}`);

                  // Use the existing deleteNode function
                  deleteNode(node.id);
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                className={`p-1 rounded transition-all group hover:scale-110 active:scale-95 ${
                  resolvedTheme === 'light'
                    ? 'hover:bg-red-600/80 text-red-600 hover:text-white'
                    : 'hover:bg-red-600/20 text-red-400 hover:text-red-300'
                }`}
                title={`Delete ${node.data.title}`}
                type="button"
              >
                <TrashIcon className="w-4 h-4 transition-transform group-hover:scale-110" />
              </button>
            </div>

            {/* Collapsible Content */}
            <div className={`transition-all duration-300 overflow-hidden ${node.data.collapsed ? 'max-h-0 opacity-0' : 'max-h-[1000px] opacity-100'}`}>
              <div className="space-y-3">
                <p className="text-[var(--text-secondary)] text-xs mb-4 font-['Figtree',sans-serif]">{node.data.description}</p>

                {/* Note Content */}
                {node.type === 'note' && (
                  <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg p-4">
                    <div className="text-[var(--text-primary)] text-sm font-['Figtree',sans-serif] whitespace-pre-line">
                      {node.data.config.content}
                    </div>
                  </div>
                )}

                {/* COMPLETELY SIMPLIFIED SDLC Tool Configuration */}
                {node.type === 'sdlc-tool' && (
                  <>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-2 font-['Figtree',sans-serif] font-medium">
                        Select Connected Integration
                      </label>
                      <div className="relative">
                        <select 
                          value={node.data.config.selectedTool || ''}
                          onChange={(e) => handleToolSelection(e.target.value)}
                          onMouseDown={(e) => e.stopPropagation()}
                          onFocus={(e) => e.stopPropagation()}
                          className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded px-3 py-2 text-[var(--text-primary)] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 hover:border-cyan-400/60 transition-all cursor-pointer appearance-none pr-8"
                          style={{ minHeight: '36px' }}
                        >
                          <option value="" className="bg-[var(--bg-secondary)] text-[var(--text-tertiary)]">
                            Choose an integration...
                          </option>
                          {connectedIntegrations.map((integration, index) => (
                            <option 
                              key={`${integration.name}-${index}`} 
                              value={integration.name}
                              className="bg-[var(--bg-secondary)] text-[var(--text-primary)] py-2"
                            >
                              {integration.name} ({integration.category})
                            </option>
                          ))}
                        </select>
                        <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)] pointer-events-none" />
                      </div>
                    </div>
                    
                    {node.data.config.selectedTool && (
                      <>
                        <div>
                          <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Tool Category</label>
                          <div className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded px-3 py-2 text-[var(--text-tertiary)] text-sm font-['Figtree',sans-serif]">
                            {node.data.config.toolCategory}
                          </div>
                        </div>
                        
                        <div>
                          <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Configuration</label>
                          <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded px-3 py-2 text-[var(--text-tertiary)] text-xs font-['Figtree',sans-serif]">
                            Tool-specific configuration will appear here based on the selected integration.
                          </div>
                        </div>
                        
                        <div>
                          <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Status</label>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span className="text-green-400 text-xs font-['Figtree',sans-serif] font-medium">Connected & Ready</span>
                          </div>
                        </div>
                      </>
                    )}
                  </>
                )}

                {/* Agent Node Configuration */}
                {node.type === 'agent' && (
                  <>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Model Provider</label>
                      <select className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded px-2 py-1 text-[var(--text-primary)] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50">
                        <option>OpenAI</option>
                        <option>Anthropic</option>
                        <option>Google</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Model Name</label>
                      <select className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded px-2 py-1 text-[var(--text-primary)] text-sm font-['Figtree',sans-serif] focus:outline-none focus:ring-2 focus:ring-cyan-400/50">
                        <option>gpt-4o</option>
                        <option>gpt-3.5-turbo</option>
                        <option>claude-3-sonnet</option>
                      </select>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">OpenAI API Key</label>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        <div className="flex-1 h-6 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded"></div>
                        <LinkIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                      </div>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Agent Instructions</label>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        <div className="text-[var(--text-tertiary)] text-xs font-['Figtree',sans-serif]">You are a helpful assistant that can...</div>
                      </div>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Tools</label>
                      <div className="text-[var(--text-tertiary)] text-xs font-['Figtree',sans-serif]">Connected SDLC tools will appear here</div>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Input</label>
                      <div className="text-[var(--text-tertiary)] text-xs font-['Figtree',sans-serif]">Receiving input</div>
                    </div>
                    <div>
                      <label className="text-[var(--text-secondary)] text-xs block mb-1 font-['Figtree',sans-serif]">Response</label>
                      <div className="text-[var(--text-tertiary)] text-xs font-['Figtree',sans-serif]">Output</div>
                    </div>
                  </>
                )}

                {/* User Input Node */}
                {node.type === 'chat-input' && (
                  <div className="text-center py-4">
                    <div className="text-[var(--text-secondary)] text-sm font-['Figtree',sans-serif]">💬 User Input</div>
                  </div>
                )}

                {/* User Output Node */}
                {node.type === 'chat-output' && (
                  <div className="text-center py-4">
                    <div className="text-[var(--text-secondary)] text-sm font-['Figtree',sans-serif]">📤 User Output</div>
                  </div>
                )}
              </div>
            </div>


          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="h-full bg-[var(--bg-primary)] text-[var(--text-primary)] flex font-['Manrope',sans-serif]">
      {/* Sidebar */}
      <div className="w-80 bg-[var(--bg-secondary)] border-r border-[var(--border-primary)] p-6 overflow-y-auto">
        {/* Add Node Section */}
        <div className="mb-6">
          <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Add Components</h3>
          <div className="space-y-3">
            {Object.entries(nodeTypes).map(([type, config]) => (
              <button
                key={type}
                onClick={() => addNode(type as keyof typeof nodeTypes)}
                className="w-full flex items-center gap-3 p-3 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg transition-all"
              >
                <div className={`w-8 h-8 rounded ${config.color} flex items-center justify-center text-white`}>
                  {config.icon}
                </div>
                <div className="text-left">
                  <div className="text-[var(--text-primary)] font-semibold text-sm">{config.title}</div>
                  <div className="text-[var(--text-secondary)] text-xs font-['Figtree',sans-serif]">{config.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Connection Instructions */}
        {isConnecting && (
          <Card className={`mb-6 ${
            resolvedTheme === 'light'
              ? 'bg-cyan-600/80 border border-cyan-600'
              : 'bg-cyan-900/20 border border-cyan-600/50'
          }`}>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className={resolvedTheme === 'light' ? 'text-white' : 'text-cyan-400'}>🔗</span>
                <span className={`font-semibold ${resolvedTheme === 'light' ? 'text-white' : 'text-cyan-300'}`}>Creating Connection</span>
              </div>
              <p className={`text-sm ${resolvedTheme === 'light' ? 'text-white' : 'text-cyan-200'}`}>Click on another node to complete the connection, or click anywhere else to cancel.</p>
            </CardContent>
          </Card>
        )}

        {/* Connected Integrations Info */}
        <Card className={resolvedTheme === 'light'
          ? 'bg-green-600/80 border border-green-600'
          : 'bg-green-900/20 border border-green-600/50'
        }>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className={resolvedTheme === 'light' ? 'text-white' : 'text-green-400'}>✅</span>
              <span className={`font-semibold ${resolvedTheme === 'light' ? 'text-white' : 'text-green-300'}`}>Connected Integrations</span>
            </div>
            <p className={`text-sm mb-2 ${resolvedTheme === 'light' ? 'text-white' : 'text-green-200'}`}>{connectedIntegrations.length} tools available for configuration</p>
            <div className={`text-xs ${resolvedTheme === 'light' ? 'text-white' : 'text-green-200'}`}>
              Including: {connectedIntegrations.slice(0, 3).map(i => i.name).join(', ')}
              {connectedIntegrations.length > 3 && ` and ${connectedIntegrations.length - 3} more...`}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Canvas */}
      <div className="flex-1 relative overflow-hidden bg-[var(--bg-primary)]">
        {/* Toolbar */}
        <div className="absolute top-6 left-6 z-30 flex items-center gap-3">
          <button className="p-3 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all">
            <PlayIcon className="w-5 h-5" />
          </button>
          <button className="p-3 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all">
            <SaveIcon className="w-5 h-5" />
          </button>
          <button className="p-3 bg-[var(--bg-primary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 rounded-lg text-[var(--text-primary)] transition-all">
            <SettingsIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Canvas */}
        <div
          ref={canvasRef}
          className="w-full h-full relative bg-[var(--bg-primary)] overflow-auto"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onClick={handleCanvasClick}
        >
          {/* Render Connections - HIGHEST Z-INDEX */}
          <svg
            className="absolute inset-0 pointer-events-none"
            style={{
              zIndex: 50,
              width: '100%',
              height: '100%',
              overflow: 'visible',
              position: 'absolute',
              top: 0,
              left: 0
            }}
          >
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="10"
                refX="9"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon points="0 0, 10 3, 0 6" fill="#22d3ee" />
              </marker>
              <marker
                id="arrowhead-selected"
                markerWidth="10"
                markerHeight="10"
                refX="9"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon points="0 0, 10 3, 0 6" fill="#67e8f9" />
              </marker>
              <marker
                id="arrowhead-temp"
                markerWidth="10"
                markerHeight="10"
                refX="9"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <polygon points="0 0, 10 3, 0 6" fill="#22d3ee" />
              </marker>
            </defs>

            {/* Existing connections */}
            {connections.map(connection => {
              const sourceNode = nodes.find(n => n.id === connection.source);
              const targetNode = nodes.find(n => n.id === connection.target);

              if (!sourceNode || !targetNode) {
                console.log(`Missing node for connection ${connection.id}:`, { sourceNode, targetNode });
                return null;
              }

              const sourcePoint = getNodeConnectionPoint(sourceNode, 'output');
              const targetPoint = getNodeConnectionPoint(targetNode, 'input');
              const path = generateCurvedPath(sourcePoint.x, sourcePoint.y, targetPoint.x, targetPoint.y);

              const isSelected = selectedConnection === connection.id;
              const strokeColor = '#22d3ee'; // Consistent cyan-400 color for all arrows
              const strokeWidth = 3; // Consistent stroke width for all arrows
              const arrowMarker = isSelected ? 'url(#arrowhead-selected)' : 'url(#arrowhead)';



              return (
                <g key={connection.id}>
                  {/* Invisible wider path for easier clicking */}
                  <path
                    d={path}
                    stroke="transparent"
                    strokeWidth="15"
                    fill="none"
                    style={{ cursor: 'pointer', pointerEvents: 'all' }}
                    onClick={(e) => handleConnectionClick(connection.id, e)}
                  />

                  {/* Bright glow effect for visibility */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={strokeWidth + 6}
                    fill="none"
                    opacity="0.3"
                    filter="blur(4px)"
                    style={{ pointerEvents: 'none' }}
                  />

                  {/* Main path with enhanced visibility */}
                  <path
                    d={path}
                    stroke={strokeColor}
                    strokeWidth={strokeWidth}
                    fill="none"
                    strokeDasharray={connection.style?.strokeDasharray}
                    markerEnd={arrowMarker}
                    opacity="1"
                    style={{
                      pointerEvents: 'none'
                    }}
                  />

                  {/* Connection start point circle */}
                  <circle
                    cx={sourcePoint.x}
                    cy={sourcePoint.y}
                    r="3"
                    fill="#22d3ee"
                    stroke={selectedConnection === connection.id ? '#67e8f9' : '#020816'}
                    strokeWidth="1"
                    style={{ pointerEvents: 'none' }}
                  />

                  {/* Connection end point circle */}
                  <circle
                    cx={targetPoint.x}
                    cy={targetPoint.y}
                    r="3"
                    fill="#22d3ee"
                    stroke={selectedConnection === connection.id ? '#67e8f9' : '#020816'}
                    strokeWidth="1"
                    style={{ pointerEvents: 'none' }}
                  />

                  {/* Connection label with enhanced visibility */}
                  {connection.label && (
                    <text
                      x={(sourcePoint.x + targetPoint.x) / 2}
                      y={(sourcePoint.y + targetPoint.y) / 2 - 15}
                      fill={strokeColor}
                      fontSize="14"
                      fontWeight="bold"
                      textAnchor="middle"
                      className="font-['Figtree',sans-serif] select-none"
                      style={{
                        pointerEvents: 'none',
                        filter: 'drop-shadow(0 0 3px rgba(0, 0, 0, 0.9))'
                      }}
                    >
                      {connection.label}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Temporary connection line while connecting */}
            {isConnecting && connectionStart && tempConnection && (
              (() => {
                const sourceNode = nodes.find(n => n.id === connectionStart.nodeId);
                if (!sourceNode) return null;

                const sourcePoint = getNodeConnectionPoint(sourceNode, connectionStart.handle as 'input' | 'output');
                const path = generateCurvedPath(sourcePoint.x, sourcePoint.y, tempConnection.x, tempConnection.y);

                return (
                  <g>
                    <path
                      d={path}
                      stroke="#22d3ee"
                      strokeWidth="6"
                      fill="none"
                      opacity="0.6"
                      filter="blur(3px)"
                    />
                    <path
                      d={path}
                      stroke="#22d3ee"
                      strokeWidth="3"
                      fill="none"
                      strokeDasharray="8,4"
                      markerEnd="url(#arrowhead-temp)"
                      className="animate-pulse"
                      style={{ 
                        filter: 'drop-shadow(0 0 4px rgba(34, 211, 238, 0.8))'
                      }}
                    />
                  </g>
                );
              })()
            )}
          </svg>

          {/* Render Nodes */}
          <div style={{ zIndex: 10, position: 'relative' }}>
            {nodes.map(node => (
              <NodeComponent key={node.id} node={node} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ToolConfigurationAgent;