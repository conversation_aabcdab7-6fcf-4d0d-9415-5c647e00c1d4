import React, { useState } from 'react';
import { NetworkIcon, ShieldCheck, <PERSON>ch, MessageSquareIcon, EditIcon, BarChart3Icon, ServerIcon, CheckIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';

export const agents = [
  {
    name: 'Tool Configuration Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <Wrench className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-blue-900/70 to-blue-600/70',
    path: '/agents/tool-configuration'
  },
  {
    name: 'Scorecards Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <ShieldCheck className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-purple-900/70 to-pink-600/70',
    path: '/agents/scorecards'
  },
  {
    name: 'Knowledge Graph Agent',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    icon: <NetworkIcon className="w-10 h-10 text-white opacity-90" />,
    gradient: 'from-blue-900/70 to-blue-600/70',
    path: '/agents/knowledge-graph'
  }
];

const buildYourOwn = {
  name: 'Build Your Own AI Agent',
  description:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
  gradient: 'from-orange-600/70 to-green-500/70',
  path: '/agents/build-your-own'
};

const PolygonBackground = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <div className="relative w-[209px] h-[241px]">
        <img className="absolute w-[184px] h-[213px] top-3.5 left-3" alt="Polygon" src="/polygon-5.svg" />
        <img className="absolute w-40 h-[185px] top-7 left-6" alt="Polygon" src="/polygon-6.svg" />
        <img className="absolute w-[136px] h-[157px] top-[42px] left-9" alt="Polygon" src="/polygon-2.svg" />
        <img className="absolute w-[111px] h-[129px] top-14 left-[49px]" alt="Polygon" src="/polygon-8.svg" />
      </div>
    </div>
  </div>
);

const AgentsPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [copiedStates, setCopiedStates] = useState<{ [key: string]: boolean }>({});

  // Theme-aware opacity for background gradients
  const gradientOpacity = resolvedTheme === 'light' ? 'opacity-40' : 'opacity-20';

  const handleMCPServerCopy = (agentName: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Mock MCP Server details - in real app this would be actual server details
    const mcpServerDetails = `MCP Server Details for ${agentName}:
Server URL: mcp://localhost:3000/${agentName.toLowerCase().replace(/\s+/g, '-')}
Protocol: Model Context Protocol v1.0
Status: Active
Capabilities: tool_calling, resource_access, prompt_templates
Authentication: Bearer token required
Last Updated: ${new Date().toISOString()}`;

    navigator.clipboard.writeText(mcpServerDetails).then(() => {
      setCopiedStates(prev => ({ ...prev, [agentName]: true }));
      
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [agentName]: false }));
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy MCP Server details:', err);
    });
  };

  return (
    <div className="min-h-screen bg-[var(--bg-primary)] px-8 py-12 font-['Manrope',sans-serif]">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        {agents.map((agent, index) => (
          <div
            key={agent.name}
            className="relative rounded-2xl overflow-hidden shadow-lg bg-[var(--bg-primary)] h-[415px] flex flex-col justify-between cursor-pointer border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all group"
          >
            {/* MCP Server Icon - Only for first 3 agents */}
            <button
              onClick={(e) => handleMCPServerCopy(agent.name, e)}
              className="absolute top-4 right-4 z-20 p-2 bg-[var(--bg-primary)]/80 backdrop-blur-sm border border-cyan-400/40 hover:border-cyan-300 rounded-lg text-cyan-400 hover:text-cyan-300 transition-all hover:scale-110 active:scale-95 group/mcp"
              title={copiedStates[agent.name] ? "MCP Server details copied!" : "Copy MCP Server details"}
            >
              {copiedStates[agent.name] ? (
                <CheckIcon className="w-4 h-4 text-green-400" />
              ) : (
                <ServerIcon className="w-4 h-4 group-hover/mcp:scale-110 transition-transform" />
              )}
            </button>

            {/* Copy Success Notification */}
            {copiedStates[agent.name] && (
              <div className="absolute top-16 right-4 z-30 px-3 py-2 bg-green-900/90 backdrop-blur-sm border border-green-600/50 rounded-lg text-green-300 text-sm font-medium animate-in fade-in slide-in-from-top-2 duration-300">
                MCP Server details copied
              </div>
            )}

            {/* Blurred gradient overlay */}
            <div className={`absolute inset-0 bg-gradient-to-br ${agent.gradient} ${gradientOpacity} blur-[75px]`}></div>
            {/* Polygon SVGs */}
            <PolygonBackground />
            {/* Icon */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              {agent.icon}
            </div>
            
            {/* Content */}
            <div className="relative z-10 px-6 pb-6 mt-auto">
              <h2 className="font-semibold text-[var(--text-primary)] text-xl leading-[24.8px] mb-3">
                {agent.name}
              </h2>
              <p className="font-normal text-[var(--text-secondary)] text-[13.5px] leading-[18.9px] mb-6">
                {agent.description}
              </p>
              
              {/* Action Buttons */}
              <div className="flex items-center justify-between gap-2">
                {/* Chat Button */}
                <Link
                  to={`/chat-sessions/new?agent=${encodeURIComponent(agent.name)}`}
                  className="flex items-center justify-center gap-2 px-3 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-all hover:scale-105 active:scale-95 flex-1 group/btn"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MessageSquareIcon className="w-4 h-4 group-hover/btn:scale-110 transition-transform" />
                  <span className="text-sm font-medium">Chat</span>
                </Link>
                
                {/* Edit Button - Only for Tool Configuration Agent */}
                {agent.name === 'Tool Configuration Agent' ? (
                  <Link
                    to="/agents/tool-configuration"
                    className="flex items-center justify-center gap-2 px-3 py-2 bg-[var(--bg-secondary)] border border-cyan-400/40 hover:bg-cyan-900/20 hover:border-cyan-300 text-cyan-300 rounded-lg transition-all hover:scale-105 active:scale-95 flex-1 group/btn"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <EditIcon className="w-4 h-4 group-hover/btn:scale-110 transition-transform" />
                    <span className="text-sm font-medium">Edit</span>
                  </Link>
                ) : (
                  <button
                    className="flex items-center justify-center gap-2 px-3 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-tertiary)] rounded-lg cursor-not-allowed opacity-50 flex-1"
                    disabled
                    onClick={(e) => e.stopPropagation()}
                  >
                    <EditIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">Edit</span>
                  </button>
                )}
                
                {/* View Executions Button */}
                <Link
                  to={`/executions?agent=${encodeURIComponent(agent.name)}`}
                  className="flex items-center justify-center gap-2 px-3 py-2 bg-[var(--bg-secondary)] border border-amber-400/40 hover:bg-amber-900/20 hover:border-amber-300 text-amber-300 rounded-lg transition-all hover:scale-105 active:scale-95 flex-1 group/btn"
                  onClick={(e) => e.stopPropagation()}
                >
                  <BarChart3Icon className="w-4 h-4 group-hover/btn:scale-110 transition-transform" />
                  <span className="text-sm font-medium">Executions</span>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
      <Link
        to={buildYourOwn.path}
        className="relative rounded-2xl overflow-hidden shadow-lg bg-[var(--bg-primary)] h-[180px] md:col-span-3 flex flex-col justify-end cursor-pointer border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all block"
      >
        {/* Blurred gradient overlay */}
        <div className={`absolute inset-0 bg-gradient-to-br ${buildYourOwn.gradient} ${gradientOpacity} blur-[75px]`}></div>
        {/* Polygon SVGs */}
        <PolygonBackground />
        {/* Content */}
        <div className="relative z-10 px-6 pb-8">
          <h2 className="font-semibold text-[var(--text-primary)] text-xl leading-[24.8px] mb-3">
            {buildYourOwn.name}
          </h2>
          <p className="font-normal text-[var(--text-secondary)] text-[13.5px] leading-[18.9px]">
            {buildYourOwn.description}
          </p>
        </div>
      </Link>
    </div>
  );
};

export default AgentsPage;