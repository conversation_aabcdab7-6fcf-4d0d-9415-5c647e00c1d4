import {
  CalendarIcon,
  ClockIcon,
  MessageSquareIcon,
  SearchIcon,
  FilterIcon,
  MoreHorizontalIcon,
  ChevronDownIcon,
  PaperclipIcon,
  MicIcon,
  XIcon,
} from "lucide-react";
import { useState } from "react";

import { Card, CardContent } from "../../components/ui/card";
import { agents } from '../Agents/index';
import { useTheme } from "../../contexts/ThemeContext";

// Mock data for chat sessions
const chatSessions = [
  {
    id: 1,
    title: "API Integration Discussion",
    description: "Discussing REST API implementation for user authentication and data management",
    timestamp: "2 hours ago",
    date: "Today",
    status: "active",
    participants: 3,
    lastMessage: "Let's implement the OAuth flow first...",
  },
  {
    id: 2,
    title: "Database Schema Review",
    description: "Reviewing the proposed database schema for the new feature set",
    timestamp: "5 hours ago",
    date: "Today",
    status: "completed",
    participants: 2,
    lastMessage: "The schema looks good, let's proceed with migration",
  },
  {
    id: 3,
    title: "Code Review Session",
    description: "Reviewing pull request #247 for the authentication module",
    timestamp: "1 day ago",
    date: "Yesterday",
    status: "pending",
    participants: 4,
    lastMessage: "Please address the security concerns mentioned",
  },
  {
    id: 4,
    title: "Performance Optimization",
    description: "Discussing strategies to improve application performance and reduce load times",
    timestamp: "2 days ago",
    date: "Dec 15",
    status: "active",
    participants: 2,
    lastMessage: "We should implement caching at the API level",
  },
  {
    id: 5,
    title: "Feature Planning Meeting",
    description: "Planning the roadmap for Q1 2024 feature releases",
    timestamp: "3 days ago",
    date: "Dec 14",
    status: "completed",
    participants: 6,
    lastMessage: "Great session! Let's document the decisions",
  },
  {
    id: 6,
    title: "Bug Triage Discussion",
    description: "Triaging critical bugs reported in the latest release",
    timestamp: "1 week ago",
    date: "Dec 10",
    status: "completed",
    participants: 3,
    lastMessage: "All critical issues have been assigned",
  },
];

export const ChatSessions = (): JSX.Element => {
  const { resolvedTheme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [selectedAgent, setSelectedAgent] = useState(agents[0]?.name || "");
  const [newSessionInput, setNewSessionInput] = useState("");
  const [showKGModal, setShowKGModal] = useState(false);

  // Theme-aware status colors
  const statusColors = {
    active: resolvedTheme === 'light'
      ? "bg-green-600/80 text-white border-green-600"
      : "bg-green-500/20 text-green-400 border-green-500/30",
    completed: resolvedTheme === 'light'
      ? "bg-blue-600/80 text-white border-blue-600"
      : "bg-blue-500/20 text-blue-400 border-blue-500/30",
    pending: resolvedTheme === 'light'
      ? "bg-yellow-600/80 text-white border-yellow-600"
      : "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
  };

  const filteredSessions = chatSessions.filter((session) => {
    const matchesSearch = session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         session.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === "all" || session.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="flex flex-col h-full bg-[var(--bg-primary)] text-[var(--text-primary)] font-['Manrope',sans-serif]">
      {/* New Session Section */}
      <div className="p-8 pb-4 border-b border-[var(--border-primary)]">
        <h2 className="font-bold text-[var(--text-primary)] text-lg mb-4">New Session</h2>
        <div className="flex items-center gap-4 mb-4">
          <div className="relative w-full max-w-xs">
            <select
              value={selectedAgent}
              onChange={e => setSelectedAgent(e.target.value)}
              className="bg-[var(--bg-secondary)] text-[var(--text-primary)] font-semibold rounded-md px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-cyan-400/60 hover:border-cyan-300 appearance-none shadow-sm transition-all w-full"
              style={{ minWidth: 180 }}
            >
              {agents.map((agent: { name: string }) => (
                <option key={agent.name} value={agent.name}>{agent.name}</option>
              ))}
            </select>
            <ChevronDownIcon className="pointer-events-none absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
          </div>
        </div>
        {/* Message Entry Area */}
        <div className="flex flex-col gap-1 mb-2">
          {/* Textarea and Send Button */}
          <div className="flex items-end bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-4 py-3">
            <textarea
              value={newSessionInput}
              onChange={e => setNewSessionInput(e.target.value)}
              placeholder="Enter your message..."
              rows={3}
              className="flex-1 bg-transparent text-[var(--text-primary)] border-none outline-none placeholder-[var(--text-tertiary)] text-base resize-none min-h-[56px] max-h-[120px]"
              style={{ minHeight: 56, maxHeight: 120 }}
            />
            <a
              href={`/chat-sessions/new?agent=${encodeURIComponent(selectedAgent)}`}
              className="ml-2 p-2 rounded-full bg-[#7678ed] hover:bg-[#6366f1] transition-colors flex items-center justify-center"
            >
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 12h14M12 5l7 7-7 7" /></svg>
            </a>
          </div>
          {/* Controls Row Below Textarea */}
          <div className="flex items-center gap-2 pt-2 pl-1">
            <button className="p-2 rounded-lg hover:bg-[var(--border-secondary)] transition-colors" title="Attach file">
              <PaperclipIcon className="w-5 h-5 text-[var(--text-tertiary)]" />
            </button>
            <button className="p-2 rounded-lg hover:bg-[var(--border-secondary)] transition-colors" title="Voice entry">
              <MicIcon className="w-5 h-5 text-[var(--text-tertiary)]" />
            </button>
            <button
              className="flex items-center gap-1 px-3 py-2 bg-[var(--bg-tertiary)] text-[var(--text-primary)] rounded-md border border-[var(--border-secondary)] text-xs font-medium hover:bg-[var(--border-secondary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]"
              onClick={() => setShowKGModal(true)}
              type="button"
            >
              Select node on Knowledge Graph
              <ChevronDownIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            </button>
            <span className="ml-auto text-xs text-[var(--text-tertiary)]">Enter to start session</span>
          </div>
          {/* Knowledge Graph Explorer Modal */}
          {showKGModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
              <div className="bg-[var(--bg-secondary)] rounded-lg shadow-lg p-8 w-full max-w-2xl relative">
                <button
                  className="absolute top-3 right-3 p-2 rounded-full hover:bg-[var(--border-secondary)]"
                  onClick={() => setShowKGModal(false)}
                  title="Close"
                >
                  <XIcon className="w-5 h-5 text-[var(--text-tertiary)]" />
                </button>
                <h3 className="text-lg font-bold mb-4 text-[var(--text-primary)]">Knowledge Graph Explorer</h3>
                <div className="text-[var(--text-secondary)]">[Knowledge Graph Explorer UI goes here]</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Divider between sections */}
      <div className="border-t border-[var(--border-primary)] my-2 mx-8" />

      {/* Previous Sessions Section */}
      <div className="flex-1 overflow-auto p-8 pt-4">
        <h2 className="font-bold text-[var(--text-primary)] text-lg mb-4">Previous Sessions</h2>
        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
            <input
              type="text"
              placeholder="Search sessions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
            />
          </div>
          <div className="flex items-center gap-2">
            <FilterIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-3 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
            >
              <option value="all">All Sessions</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
        {/* Sessions List */}
        <div className="space-y-4">
          {filteredSessions.map((session) => (
            <Card
              key={session.id}
              className="bg-[var(--bg-secondary)] border border-cyan-400/40 hover:ring-2 hover:ring-cyan-300/80 transition-all cursor-pointer group"
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <MessageSquareIcon className="w-5 h-5 text-[#7678ed]" />
                      <h3 className="font-semibold text-[var(--text-primary)] text-lg leading-[22.3px]">
                        {session.title}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${statusColors[session.status as keyof typeof statusColors]}`}>
                        {session.status}
                      </span>
                    </div>
                    
                    <p className="font-normal text-[var(--text-secondary)] text-sm leading-[19.6px] mb-3">
                      {session.description}
                    </p>
                    
                    <div className="flex items-center gap-4 text-[var(--text-tertiary)] text-sm">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="w-4 h-4" />
                        <span>{session.date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ClockIcon className="w-4 h-4" />
                        <span>{session.timestamp}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-4 h-4 rounded-full bg-[#7678ed] flex items-center justify-center text-xs text-white">
                          {session.participants}
                        </div>
                        <span>{session.participants} participants</span>
                      </div>
                    </div>
                    
                    {session.lastMessage && (
                      <div className="mt-3 p-3 bg-[var(--bg-primary)] rounded-lg border border-[var(--border-primary)]">
                        <p className="font-normal text-[var(--text-secondary)] text-sm leading-[19.6px] italic">
                          "{session.lastMessage}"
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <button className="opacity-0 group-hover:opacity-100 transition-opacity p-2 hover:bg-[var(--bg-primary)] rounded-lg">
                    <MoreHorizontalIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                  </button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        {filteredSessions.length === 0 && (
          <div className="text-center py-12">
            <MessageSquareIcon className="w-12 h-12 text-[var(--text-tertiary)] mx-auto mb-4" />
            <h3 className="font-semibold text-[var(--text-primary)] text-lg mb-2">
              No sessions found
            </h3>
            <p className="font-normal text-[var(--text-secondary)] text-sm">
              {searchQuery ? "Try adjusting your search terms" : "Start a new chat session to get started"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};