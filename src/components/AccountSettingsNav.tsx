import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ChevronLeftIcon, ChevronRightIcon, WrenchIcon, WorkflowIcon } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { useState, useRef } from 'react';
import ThemeToggle from './ThemeToggle';

const navItems = [
  {
    label: 'Tool Integrations',
    path: '/settings/tool-integrations',
    icon: <WrenchIcon className="w-5 h-5 mr-2" />,
  },
  {
    label: 'Runners',
    path: '/settings/runners',
    icon: <WorkflowIcon className="w-5 h-5 mr-2" />,
  },
];


interface AccountSettingsNavProps {
  isCollapsed: boolean;
  setIsCollapsed: (v: boolean | ((prev: boolean) => boolean)) => void;
}

export default function AccountSettingsNav({ isCollapsed, setIsCollapsed }: AccountSettingsNavProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [isThemeToggleOpen, setIsThemeToggleOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);
  
  // Try to go back to where the user came from, fallback to home
  const from = location.state?.from || '/';

  const handleProfileClick = () => {
    setIsThemeToggleOpen(!isThemeToggleOpen);
  };

  return (
    <aside className={`${isCollapsed ? 'w-[60px]' : 'w-56'} bg-[var(--bg-secondary)] min-h-screen flex flex-col transition-all duration-200`}>
      {/* Header with Logo and Collapse Button */}
      <div className={`flex items-center ${isCollapsed ? 'flex-col justify-center' : 'px-7'} py-5 border-b border-[var(--border-primary)] bg-gradient-to-b from-[rgba(118,120,237,0.05)] to-[rgba(118,120,237,0.05)] bg-[var(--border-primary)]`} style={{ minHeight: 82 }}>
        {/* Logo - clickable when collapsed */}
        <div
          className={`inline-flex items-center gap-3.5 ${isCollapsed ? 'cursor-pointer' : ''}`}
          onClick={isCollapsed ? () => setIsCollapsed(false) : undefined}
          title={isCollapsed ? 'Expand sidebar' : undefined}
        >
          <div className="relative w-10 h-10 bg-[url(/subtract.svg)] bg-[100%_100%]" />
          {!isCollapsed && (
            <div className="font-['Manrope',Helvetica] font-semibold text-[var(--text-primary)] text-lg leading-[22.3px]">
              AccelOS
            </div>
          )}
        </div>

        {/* Collapse/Expand Button - only show when expanded */}
        {!isCollapsed && (
          <button
            className="ml-auto p-1 hover:bg-[var(--bg-tertiary)] transition-colors rounded"
            onClick={() => setIsCollapsed(true)}
            title="Collapse sidebar"
          >
            <ChevronLeftIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
          </button>
        )}
      </div>
      {/* Back Link */}
      <button
        onClick={() => navigate(from)}
        className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer border-b border-[var(--bg-tertiary)] bg-[var(--bg-secondary)]`}
        style={{ outline: 'none', boxShadow: 'none' }}
      >
        <div className="inline-flex items-center gap-2.5 p-2.5 rounded-lg text-[var(--text-tertiary)]">
          <ChevronLeftIcon className="w-5 h-5" />
        </div>
        {!isCollapsed && (
          <div className="flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] text-[var(--text-secondary)]">Back to Main Nav</div>
        )}
      </button>
      {/* Settings Nav */}
      <nav className={`flex flex-col gap-2 flex-1 mt-6 ${isCollapsed ? 'px-0 items-center' : 'px-2'}`}>
        {navItems.map(item => {
          const isActive = location.pathname === item.path;
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer`}
            >
              <div className={`inline-flex items-center gap-2.5 p-2.5 rounded-lg ${isActive ? 'text-[#7678ed]' : 'text-[var(--text-tertiary)]'}`}>
                {item.icon}
              </div>
              {!isCollapsed && (
                <div className={`flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] ${isActive ? 'text-[#7678ed]' : 'text-[var(--text-secondary)]'}`}>{item.label}</div>
              )}
            </Link>
          );
        })}
      </nav>
      {/* User Profile at bottom */}
      <div 
        ref={profileRef}
        className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-4 px-7'} py-5 border-t border-[var(--border-primary)] bg-[var(--border-primary)] mt-auto cursor-pointer hover:bg-[var(--bg-tertiary)] transition-colors`}
        onClick={handleProfileClick}
      >
        <Avatar className="w-[41.5px] h-[41.5px]">
          <AvatarImage src="/profile.svg" alt="John Smith" />
          <AvatarFallback>JS</AvatarFallback>
        </Avatar>
        {!isCollapsed && (
          <div className="flex-1 font-['Figtree',Helvetica] font-normal text-[var(--text-secondary)] text-base leading-[19.8px]">
            John Smith
          </div>
        )}
      </div>

      {/* Theme Toggle */}
      <ThemeToggle
        isOpen={isThemeToggleOpen}
        onClose={() => setIsThemeToggleOpen(false)}
        triggerRef={profileRef}
      />
    </aside>
  );
}