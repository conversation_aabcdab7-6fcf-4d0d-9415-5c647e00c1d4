import {
  GitBranchIcon,
  HelpCircleIcon,
  HomeIcon,
  NetworkIcon,
  MessageSquareIcon,
  PieChartIcon,
  SettingsIcon,
  BotIcon,
  ClipboardCheckIcon,
  PlayIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  UsersIcon,
} from "lucide-react";
import React, { useState, useRef } from "react";
import { useLocation, Link } from "react-router-dom";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../ui/avatar";
import ThemeToggle from "../ThemeToggle";

// Navigation menu data
const navigationItems = [
  {
    title: null,
    items: [
      {
        icon: <HomeIcon className="w-6 h-6" />,
        label: "My Home",
        path: "/",
      },
      {
        icon: <UsersIcon className="w-6 h-6" />,
        label: "Onboarding",
        path: "/onboarding",
      },
    ],
  },
  {
    title: "AUTOMATE SDLC",
    items: [
      {
        icon: <MessageSquareIcon className="w-6 h-6" />,
        label: "Chat Sessions",
        path: "/chat-sessions",
      },
      {
        icon: <BotIcon className="w-6 h-6" />,
        label: "AI Agents",
        path: "/agents",
      },
      {
        icon: <PieChartIcon className="w-6 h-6" />,
        label: "Executions",
        path: "/executions",
      },
    ],
  },
  {
    title: "EXPLORE",
    items: [
      {
        icon: <NetworkIcon className="w-6 h-6" />,
        label: "Eng Knowledge Graph",
        path: "/knowledge-graph",
      },
      {
        icon: <ClipboardCheckIcon className="w-6 h-6" />,
        label: "Scorecards",
        path: "/scorecards",
      },
    ],
  },

  {
    title: null,
    items: [], // First empty section
  },

  {
    title: null,
    items: [], // Second empty section
  },

  {
    title: null,
    items: [
      {
        icon: <SettingsIcon className="w-6 h-6" />,
        label: "Account Settings",
        path: "/settings",
      },
      {
        icon: <HelpCircleIcon className="w-6 h-6" />,
        label: "Help",
        path: "/help",
      },
    ],
  },
];

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}

export const Layout = ({ children, title, subtitle }: LayoutProps): JSX.Element => {
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isThemeToggleOpen, setIsThemeToggleOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);

  const handleProfileClick = () => {
    setIsThemeToggleOpen(!isThemeToggleOpen);
  };

  return (
    <div className="bg-[var(--bg-primary)] flex flex-row justify-center w-full min-h-screen h-screen">
      <div className="bg-[var(--bg-primary)] overflow-hidden w-full max-w-[1728px] min-h-screen h-screen relative">
        {/* Header */}
        <header className={`absolute w-full h-[82px] top-0 left-0 bg-[var(--bg-primary)] border-b-[1.5px] border-[var(--border-primary)] transition-all duration-200`}>
          <div className={`flex flex-col items-start gap-1 p-5 ${isCollapsed ? 'ml-[60px]' : 'ml-[250px]'} transition-all duration-200`}>
            <h1 className="font-['Manrope',Helvetica] font-bold text-[var(--text-primary)] text-lg leading-[22.3px]">
              {title || "Welcome, John Smith!"}
            </h1>
            <p className="font-['Figtree',Helvetica] font-normal text-[var(--text-secondary)] text-sm leading-[19.6px]">
              {subtitle || "Senior Platform Engineer"}
            </p>
          </div>
        </header>

        {/* Sidebar */}
        <aside className={`fixed ${isCollapsed ? 'w-[60px]' : 'w-[250px]'} h-[100vh] top-0 left-0 transition-all duration-200 bg-[var(--bg-secondary)] z-40 flex flex-col`}>
          {/* Header with Logo and Collapse Button */}
          <div className={`flex items-center ${isCollapsed ? 'flex-col justify-center' : 'justify-between px-7'} py-5 border-b border-[var(--border-primary)] bg-gradient-to-b from-[rgba(118,120,237,0.05)] to-[rgba(118,120,237,0.05)], bg-[var(--border-primary)]`} style={{ minHeight: 82 }}>
            {/* Logo - clickable when collapsed */}
            <div
              className={`inline-flex items-center gap-3.5 ${isCollapsed ? 'cursor-pointer' : ''}`}
              onClick={isCollapsed ? () => setIsCollapsed(false) : undefined}
              title={isCollapsed ? 'Expand sidebar' : undefined}
            >
              <div className="relative w-10 h-10 bg-[url(/subtract.svg)] bg-[100%_100%]" />
              {!isCollapsed && (
                <div className="font-['Manrope',Helvetica] font-semibold text-[var(--text-primary)] text-lg leading-[22.3px]">
                  AccelOS
                </div>
              )}
            </div>

            {/* Collapse/Expand Button - only show when expanded */}
            {!isCollapsed && (
              <button
                className="w-7 h-7 flex items-center justify-center bg-[var(--border-secondary)] border border-[var(--border-primary)] rounded-full shadow hover:bg-[var(--bg-tertiary)] transition-colors"
                onClick={() => setIsCollapsed(true)}
                title="Collapse sidebar"
              >
                <ChevronLeftIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
              </button>
            )}
          </div>
          {/* Navigation */}
          <nav className="flex flex-col flex-1">
            {navigationItems.map((section, sectionIndex) => (
              <div key={sectionIndex} className="flex flex-col gap-1 mb-2">
                {!isCollapsed && section.title && (
                  <div className="px-7 py-0">
                    <h3 className="font-['Figtree',Helvetica] font-semibold text-[var(--text-tertiary)] text-[13px] leading-[18.2px] opacity-50">
                      {section.title}
                    </h3>
                  </div>
                )}
                {section.items.map((item, itemIndex) => {
                  const isActive = location.pathname === item.path;
                  return (
                    <Link
                      key={itemIndex}
                      to={item.path}
                      className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-2.5 pl-7 pr-3'} py-1.5 cursor-pointer`}
                    >
                      <div className={`inline-flex items-center gap-2.5 p-2.5 rounded-lg ${isActive ? 'text-[#7678ed]' : 'text-[var(--text-tertiary)]'}`}>{item.icon}</div>
                      {!isCollapsed && (
                        <div className={`flex-1 font-['Figtree',Helvetica] font-normal text-sm leading-[17px] ${isActive ? 'text-[#7678ed]' : 'text-[var(--text-secondary)]'}`}>{item.label}</div>
                      )}
                    </Link>
                  );
                })}
              </div>
            ))}
          </nav>
          {/* User Profile */}
          <div 
            ref={profileRef}
            className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-4 px-7'} py-5 border-t border-[var(--border-primary)] bg-[var(--border-primary)] cursor-pointer hover:bg-[var(--bg-tertiary)] transition-colors`}
            onClick={handleProfileClick}
          >
            <Avatar className="w-[41.5px] h-[41.5px]">
              <AvatarImage src="/profile.svg" alt="John Smith" />
              <AvatarFallback>JS</AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div className="flex-1 font-['Figtree',Helvetica] font-normal text-[var(--text-secondary)] text-base leading-[19.8px]">
                John Smith
              </div>
            )}
          </div>
        </aside>

        {/* Main Content - NO GAP, PERFECT ALIGNMENT */}
        <main className={`absolute top-[82px] ${isCollapsed ? 'left-[60px]' : 'left-[250px]'} right-0 bottom-0 overflow-auto transition-all duration-200`}>
          {children}
        </main>

        {/* Theme Toggle */}
        <ThemeToggle
          isOpen={isThemeToggleOpen}
          onClose={() => setIsThemeToggleOpen(false)}
          triggerRef={profileRef}
        />
      </div>
    </div>
  );
};